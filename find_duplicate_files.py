#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import hashlib
import logging
import time
import json
from datetime import datetime
from tqdm import tqdm
import shutil
import concurrent.futures

# 配置项
CONFIG = {
    # 是否跨文件夹对比
    'cross_folder_comparison': True,

    # 要扫描的目录列表
    'scan_directories': [
        #'Z:\Binance', #zip路径
        'W:\\', #图书馆
        #'H:\\',  #csv路径
        #'D:\\360Downloads', #baidudownload
        #'X:\\【资料库】柚子',
        #'U:\\',  #cpa
        #'X:\【研究】nynx', #nynx
        #'X:\\', #量化投资
        #'X:\【资料库】柚子',
        #'Z:\\',
        #可以添加多个目录W:\【azw3】, W:\【mobi】, W:\【epub】, W:\【txt】,W:\【pdf】,W:\【chm】
    ],

    # 删除模式配置
    'delete_mode': {
        # 删除模式: 'auto' | 'confirm_each' | 'batch_confirm' | 'smart_auto'
        'mode': 'auto',  # 推荐：智能自动模式

        # 智能自动删除：当选择理由明确时自动删除，模糊时询问
        'smart_auto_patterns': [
            'temp',     # 临时目录
            'cache',    # 缓存目录
            '下载',     # 下载目录
            '副本',     # 副本目录
            '复制',     # 复制目录
            '文件名模式判断(高置信度)',  # 明确的文件名副本模式
            '文件名模式判断(中等置信度)',  # 中等置信度的文件名副本模式
        ],

        # 批量确认时每批处理的数量
        'batch_size': 10,
    },

    # 日志文件路径
    'log_file': 'duplicate_files_log.txt',

    # 重复文件名模式配置
    'duplicate_patterns': {
        # 严格模式：完全匹配的副本模式
        'strict_patterns': [
            r'\(\d+\)$',      # 匹配 "(1)", "(2)" 等
            r'副本$',         # 匹配 "副本"
            r'复制$',         # 匹配 "复制"
            r'copy$',         # 匹配 "copy"
            r'_\d+$',         # 匹配 "_1", "_2" 等
            r'_copy$',        # 匹配 "_copy"
            r'_副本$',        # 匹配 "_副本"
            r'_复制$',        # 匹配 "_复制"
        ],

        # 宽松模式：相似文件名模式（需要更仔细的判断）
        'loose_patterns': [
            r'[._-]\d+$',     # 匹配 ".1", "_1", "-1" 等
            r'[._-]v\d+$',    # 匹配 ".v1", "_v2", "-v3" 等
            r'[._-]ver\d+$',  # 匹配 ".ver1", "_ver2" 等
            r'[._-]backup$',  # 匹配 ".backup", "_backup" 等
            r'[._-]bak$',     # 匹配 ".bak", "_bak" 等
            r'[._-]old$',     # 匹配 ".old", "_old" 等
            r'[._-]new$',     # 匹配 ".new", "_new" 等
            r'[._-]temp$',    # 匹配 ".temp", "_temp" 等
            r'[._-]tmp$',     # 匹配 ".tmp", "_tmp" 等
        ],

        # 是否启用宽松模式（可能会有误判，建议配合哈希验证）
        'enable_loose_mode': True,

        # 宽松模式下是否强制要求哈希验证
        'loose_mode_require_hash': True,
    },

    # 是否计算文件哈希值进行额外验证（会降低速度但提高准确性）
    'verify_with_hash': False,

    # 调试模式：显示详细的处理信息
    'debug_mode': False,

    # 性能优化配置
    'performance': {
        # 最小文件大小（字节），小于此大小的文件将被忽略
        'min_file_size': 1024,  # 1KB

        # 最大文件大小（字节），大于此大小的文件将强制使用哈希验证
        'max_file_size_no_hash': 100 * 1024 * 1024,  # 100MB

        # 文件类型过滤（扩展名列表，为空则不过滤）
        'file_extensions': [],  # 例如: ['.txt', '.doc', '.pdf']

        # 排除的文件类型
        'exclude_extensions': ['.tmp', '.log', '.cache'],

        # 是否缓存文件哈希值
        'cache_hashes': True,

        # 哈希缓存文件路径
        'hash_cache_file': 'file_hash_cache.json',
    },

    # 状态文件路径
    'state_file': 'duplicate_finder_state.json',

    # 文件选择策略配置
    'file_selection_strategy': {
        # 优先保留的目录模式（正则表达式，优先级从高到低）
        'priority_directories': [
            r'.*[/\\]原始[/\\].*',      # 包含"原始"的目录
            r'.*[/\\]master[/\\].*',    # 包含"master"的目录
            r'.*[/\\]main[/\\].*',      # 包含"main"的目录
            r'.*[/\\]backup[/\\].*',    # 包含"backup"的目录（较低优先级）
        ],

        # 优先删除的目录模式（正则表达式）
        'delete_priority_directories': [
            r'.*[/\\]temp[/\\].*',      # 临时目录
            r'.*[/\\]tmp[/\\].*',       # 临时目录
            r'.*[/\\]cache[/\\].*',     # 缓存目录
            r'.*[/\\]副本[/\\].*',      # 包含"副本"的目录
            r'.*[/\\]复制[/\\].*',      # 包含"复制"的目录
            r'.*[/\\]copy[/\\].*',      # 包含"copy"的目录
            r'.*[/\\]下载[/\\].*',      # 下载目录
            r'.*[/\\]download[/\\].*',  # 下载目录
        ],

        # 是否考虑文件修改时间（True: 保留较新的文件）
        'prefer_newer_files': True,

        # 是否考虑目录深度（True: 保留目录层级较浅的文件）
        'prefer_shallow_directories': True,

        # 是否显示选择理由
        'show_selection_reason': True,
    },
}

# 设置日志
def setup_logging():
    log_file = CONFIG['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('duplicate_finder')

# 哈希缓存
_hash_cache = {}
_hash_cache_loaded = False

# 脚本状态
_script_state = {
    'processed_directories': [],
    'duplicate_groups': [], # duplicate_groups 现在存储更详细的元组 (original, duplicate, reason, confidence, type)
    'deleted_files_set': set(),
}
_state_loaded = False

def load_hash_cache():
    """加载哈希缓存"""
    global _hash_cache, _hash_cache_loaded
    if _hash_cache_loaded:
        return

    cache_file = CONFIG['performance']['hash_cache_file']
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                _hash_cache = json.load(f)
        except Exception:
            _hash_cache = {}
    _hash_cache_loaded = True

def save_hash_cache():
    """保存哈希缓存"""
    if not CONFIG['performance']['cache_hashes']:
        return

    cache_file = CONFIG['performance']['hash_cache_file']
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(_hash_cache, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.getLogger('duplicate_finder').warning(f"保存哈希缓存失败: {e}")

def load_state():
    """加载脚本状态"""
    global _script_state, _state_loaded, _deleted_files_set
    if _state_loaded:
        return

    state_file = CONFIG['state_file']
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                loaded_state = json.load(f)
                _script_state['processed_directories'] = loaded_state.get('processed_directories', [])
                # 确保加载的 duplicate_groups 包含所有五个元素，处理旧格式兼容性
                loaded_duplicate_groups = loaded_state.get('duplicate_groups', [])
                # 检查第一个元素（如果存在）的长度来判断是否是新格式
                if loaded_duplicate_groups and isinstance(loaded_duplicate_groups[0], (list, tuple)) and len(loaded_duplicate_groups[0]) == 5:
                     _script_state['duplicate_groups'] = loaded_duplicate_groups
                else:
                     # 如果是旧格式或空，保留旧数据或初始化为空列表
                     # 对于旧格式 (original, duplicate)，我们无法恢复 reason, confidence, type
                     # 此时选择忽略旧的重复组列表，从头查找，或者提供一个迁移选项
                     # 为了简单起见，如果格式不匹配，我们发出警告并从空列表开始
                     logging.getLogger('duplicate_finder').warning(f"状态文件 {state_file} 中的重复组格式不匹配，将忽略已保存的重复组列表。")
                     _script_state['duplicate_groups'] = []

                # 将 deleted_files_set 从列表转换回集合
                _script_state['deleted_files_set'] = set(loaded_state.get('deleted_files_set', []))
                _deleted_files_set = _script_state['deleted_files_set'] # 更新全局使用的集合
            logging.getLogger('duplicate_finder').info(f"成功加载状态文件: {state_file}")
            _state_loaded = True
        except json.JSONDecodeError as e:
            logging.getLogger('duplicate_finder').error(f"加载状态文件 {state_file} 失败，JSON 格式错误: {e}")
            # 处理 JSON 格式错误，可能意味着文件损坏
            print(f"错误：无法读取状态文件 {state_file}，文件可能已损坏或格式不正确。错误信息：{e}")
            print("您可以选择删除状态文件并从头开始 (输入 'delete')，或按任意键退出。")
            choice = input().strip().lower()
            if choice == 'delete':
                try:
                    os.remove(state_file)
                    print(f"已删除状态文件: {state_file}，请重新运行脚本")
                    # 重置状态，以便从头开始
                    _script_state = {
                        'processed_directories': [],
                        'duplicate_groups': [],
                        'deleted_files_set': set(),
                    }
                    _deleted_files_set = _script_state['deleted_files_set']
                    _state_loaded = False # 标记加载失败
                except Exception as delete_e:
                    print(f"错误：删除状态文件失败: {delete_e}，请手动删除 {state_file}")
                    logging.getLogger('duplicate_finder').error(f"删除状态文件 {state_file} 失败: {delete_e}")
                    exit()
            else:
                exit()
        except Exception as e:
            logging.getLogger('duplicate_finder').error(f"加载状态文件 {state_file} 失败: {e}")
            print(f"错误：加载状态文件 {state_file} 失败。错误信息：{e}")
            print("请检查文件权限或手动删除状态文件，然后重试。")
            # 重置状态，以便从头开始（即使加载失败也重置）
            _script_state = {
                'processed_directories': [],
                'duplicate_groups': [],
                'deleted_files_set': set(),
            }
            _deleted_files_set = _script_state['deleted_files_set']
            _state_loaded = False # 标记加载失败
            # 根据情况决定是否退出，这里选择不立即退出，让脚本继续（可能从头开始）
            # exit()
    else:
        logging.getLogger('duplicate_finder').info(f"未找到状态文件: {state_file}, 将从头开始")
        _state_loaded = False # 标记未加载

def save_state(state_data, reason=""):
    """保存脚本状态"""
    state_file = CONFIG['state_file']
    try:
        # 准备保存的数据，将集合转换为列表
        data_to_save = {
            'processed_directories': state_data.get('processed_directories', []),
            'duplicate_groups': state_data.get('duplicate_groups', []), # 保存包含详细信息的重复组列表
            'deleted_files_set': list(state_data.get('deleted_files_set', [])), # 将集合转换为列表
        }
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        logging.getLogger('duplicate_finder').info(f"状态已保存到 {state_file} ({reason})")
    except Exception as e:
        logging.getLogger('duplicate_finder').warning(f"保存状态失败: {e}")

def get_file_hash(file_path, block_size=65536):
    """获取文件的哈希值，支持缓存"""
    try:
        # 获取文件信息用于缓存键
        stat = os.stat(file_path)
        file_size = stat.st_size
        file_mtime = stat.st_mtime

        # 创建缓存键
        cache_key = f"{file_path}:{file_size}:{file_mtime}"

        # 检查缓存
        if CONFIG['performance']['cache_hashes']:
            load_hash_cache()
            if cache_key in _hash_cache:
                return _hash_cache[cache_key]

        # 计算哈希值
        hasher = hashlib.blake2b()
        with open(file_path, 'rb') as f:
            buf = f.read(block_size)
            while len(buf) > 0:
                hasher.update(buf)
                buf = f.read(block_size)

        hash_value = hasher.hexdigest()

        # 保存到缓存
        if CONFIG['performance']['cache_hashes']:
            _hash_cache[cache_key] = hash_value

        return hash_value
    except Exception as e:
        return None

# 智能选择要保留的文件
def smart_file_selection(path1, path2):
    """
    智能选择要保留的文件，返回 (original_file, duplicate_file, reason, confidence, type)
    优先级顺序：
    1. 文件名模式判断（最高优先级）
    2. 目录优先级
    3. 文件修改时间
    4. 目录深度
    5. 路径长度
    """
    strategy = CONFIG['file_selection_strategy']

    # 获取文件名（不含路径）
    name1 = os.path.basename(path1)
    name2 = os.path.basename(path2)

    # 0. 最高优先级：文件名模式判断
    # 检查是否有明确的副本文件名模式
    is_dup_1_2, conf_1_2, type_1_2 = is_duplicate_filename(name1, name2)  # name2是name1的副本
    is_dup_2_1, conf_2_1, type_2_1 = is_duplicate_filename(name2, name1)  # name1是name2的副本

    if is_dup_1_2 and conf_1_2 == 'high':  # 高置信度的副本模式
        reason = f"文件名模式判断(高置信度): '{name2}' 是 '{name1}' 的副本"
        return path1, path2, reason, 'high', type_1_2
    elif is_dup_2_1 and conf_2_1 == 'high':  # 高置信度的副本模式
        reason = f"文件名模式判断(高置信度): '{name1}' 是 '{name2}' 的副本"
        return path2, path1, reason, 'high', type_2_1

    # 1. 检查优先保留的目录模式
    for i, pattern in enumerate(strategy['priority_directories']):
        path1_match = re.search(pattern, path1, re.IGNORECASE)
        path2_match = re.search(pattern, path2, re.IGNORECASE)

        if path1_match and not path2_match:
            reason = f"路径1匹配优先保留目录模式: {pattern}"
            return path1, path2, reason, 'high', 'directory_priority'
        elif path2_match and not path1_match:
            reason = f"路径2匹配优先保留目录模式: {pattern}"
            return path2, path1, reason, 'high', 'directory_priority'

    # 2. 检查优先删除的目录模式
    for pattern in strategy['delete_priority_directories']:
        path1_match = re.search(pattern, path1, re.IGNORECASE)
        path2_match = re.search(pattern, path2, re.IGNORECASE)

        if path1_match and not path2_match:
            reason = f"路径1匹配优先删除目录模式: {pattern}"
            return path2, path1, reason, 'high', 'delete_directory_priority'
        elif path2_match and not path1_match:
            reason = f"路径2匹配优先删除目录模式: {pattern}"
            return path1, path2, reason, 'high', 'delete_directory_priority'

    # 2.5. 中等置信度的文件名模式判断（在目录优先级之后）
    if is_dup_1_2 and conf_1_2 == 'medium':  # 中等置信度的副本模式
        reason = f"文件名模式判断(中等置信度): '{name2}' 可能是 '{name1}' 的副本"
        return path1, path2, reason, 'medium', type_1_2
    elif is_dup_2_1 and conf_2_1 == 'medium':  # 中等置信度的副本模式
        reason = f"文件名模式判断(中等置信度): '{name1}' 可能是 '{name2}' 的副本"
        return path2, path1, reason, 'medium', type_2_1

    # 3. 考虑文件修改时间
    if strategy['prefer_newer_files']:
        try:
            mtime1 = os.path.getmtime(path1)
            mtime2 = os.path.getmtime(path2)

            if abs(mtime1 - mtime2) > 60:  # 相差超过1分钟才考虑
                if mtime1 > mtime2:
                    reason = f"路径1文件更新 (修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime1))})"
                    return path1, path2, reason, 'medium', 'newer_file' # 修改时间判断置信度为 medium
                else:
                    reason = f"路径2文件更新 (修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime2))})"
                    return path2, path1, reason, 'medium', 'newer_file' # 修改时间判断置信度为 medium
        except Exception:
            pass

    # 4. 考虑目录深度
    if strategy['prefer_shallow_directories']:
        depth1 = path1.count(os.sep)
        depth2 = path2.count(os.sep)

        if depth1 != depth2:
            if depth1 < depth2:
                reason = f"路径1目录层级较浅 (深度: {depth1} vs {depth2})"
                return path1, path2, reason, 'medium', 'shallow_directory' # 目录深度判断置信度为 medium
            else:
                reason = f"路径2目录层级较浅 (深度: {depth2} vs {depth1})"
                return path2, path1, reason, 'medium', 'shallow_directory' # 目录深度判断置信度为 medium

    # 5. 最后考虑路径长度（作为最后的判断标准）
    if len(path1) <= len(path2):
        reason = f"路径1较短 (长度: {len(path1)} vs {len(path2)})"
        return path1, path2, reason, 'low', 'path_length' # 路径长度判断置信度为 low
    else:
        reason = f"路径2较短 (长度: {len(path2)} vs {len(path1)})"
        return path2, path1, reason, 'low', 'path_length' # 路径长度判断置信度为 low

# 检查文件名是否是原始文件的副本
def is_duplicate_filename(original, potential_duplicate):
    """
    检查文件名是否是原始文件的副本
    返回: (is_duplicate, confidence, pattern_type)
    - is_duplicate: 是否是副本
    - confidence: 置信度 ('high' | 'medium' | 'low')
    - pattern_type: 匹配的模式类型 ('strict' | 'loose')
    """
    # 获取文件名和扩展名
    original_name, original_ext = os.path.splitext(original)
    dup_name, dup_ext = os.path.splitext(potential_duplicate)

    # 扩展名必须相同
    if original_ext.lower() != dup_ext.lower():
        return False, 'low', None

    # 检查是否原始文件名是潜在副本的前缀
    if not dup_name.startswith(original_name):
        return False, 'low', None

    # 检查后缀是否匹配任何重复模式
    suffix = dup_name[len(original_name):]
    if not suffix:
        return False, 'low', None

    patterns_config = CONFIG['duplicate_patterns']

    # 首先检查严格模式
    for pattern in patterns_config['strict_patterns']:
        if re.search(pattern, suffix, re.IGNORECASE):
            return True, 'high', 'strict'

    # 如果启用了宽松模式，检查宽松模式
    if patterns_config['enable_loose_mode']:
        for pattern in patterns_config['loose_patterns']:
            if re.search(pattern, suffix, re.IGNORECASE):
                confidence = 'medium' if patterns_config['loose_mode_require_hash'] else 'low'
                return True, confidence, 'loose'

    return False, 'low', None

# 兼容性函数：保持原有接口
def is_duplicate_filename_simple(original, potential_duplicate):
    """简化版本，只返回布尔值，保持向后兼容"""
    is_dup, confidence, pattern_type = is_duplicate_filename(original, potential_duplicate)
    return is_dup

# 文件过滤函数
def should_process_file(file_path, file_size):
    """判断文件是否应该被处理"""
    perf_config = CONFIG['performance']

    # 检查文件大小
    if file_size < perf_config['min_file_size']:
        return False

    # 检查文件扩展名
    file_ext = os.path.splitext(file_path)[1].lower()

    # 检查排除的扩展名
    if file_ext in perf_config['exclude_extensions']:
        return False

    # 检查包含的扩展名（如果设置了）
    if perf_config['file_extensions'] and file_ext not in perf_config['file_extensions']:
        return False

    return True

# 收集目录中的所有文件
def collect_files(directory):
    files_info = []
    processed_files = 0
    skipped_files = 0

    # 创建一个不带总数的进度条，因为它需要在遍历过程中动态更新
    pbar = tqdm(desc=f"收集文件信息 ({os.path.basename(directory) or directory})", unit="文件")

    for root, _, files in os.walk(directory):
        for file in files:
            try:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)

                # 应用文件过滤
                if not should_process_file(file_path, file_size):
                    skipped_files += 1
                    continue

                file_name = os.path.basename(file_path)
                folder = os.path.dirname(file_path)

                files_info.append((file_path, file_size, file_name, folder))

                processed_files += 1
                pbar.update(1) # 每处理一个文件更新一次进度条
            except Exception:
                # 忽略无法访问的文件
                skipped_files += 1
                pass

    pbar.close()
    return files_info, processed_files, skipped_files

# 配置验证函数
def validate_config():
    """验证配置的有效性"""
    errors = []

    # 检查扫描目录
    if not CONFIG['scan_directories']:
        errors.append("scan_directories 不能为空")

    for directory in CONFIG['scan_directories']:
        if not os.path.exists(directory):
            errors.append(f"扫描目录不存在: {directory}")

    # 检查删除模式
    valid_modes = ['auto', 'confirm_each', 'batch_confirm', 'smart_auto']
    if CONFIG['delete_mode']['mode'] not in valid_modes:
        errors.append(f"无效的删除模式: {CONFIG['delete_mode']['mode']}")

    # 检查批量大小
    if CONFIG['delete_mode']['batch_size'] <= 0:
        errors.append("batch_size 必须大于 0")

    # 检查文件大小设置
    perf = CONFIG['performance']
    if perf['min_file_size'] < 0:
        errors.append("min_file_size 不能为负数")

    if perf['max_file_size_no_hash'] < perf['min_file_size']:
        errors.append("max_file_size_no_hash 不能小于 min_file_size")

    if errors:
        raise ValueError("配置错误:\n" + "\n".join(f"- {error}" for error in errors))

# 扫描目录并查找重复文件
def find_duplicates():
    # 验证配置
    validate_config()

    logger = setup_logging()
    logger.info(f"开始查找重复文件，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"跨文件夹对比: {'启用' if CONFIG['cross_folder_comparison'] else '禁用'}")

    # 显示性能配置信息
    perf = CONFIG['performance']
    logger.info(f"性能配置: 最小文件大小={perf['min_file_size']}字节, 排除扩展名={perf['exclude_extensions']}")

    # 尝试加载之前的状态
    load_state()
    processed_directories = set(_script_state['processed_directories'])
    duplicate_groups = _script_state['duplicate_groups'] # 从状态中加载已找到的重复组
    global _deleted_files_set
    _deleted_files_set = _script_state['deleted_files_set'] # 从状态中加载已删除的集合

    # 存储所有文件的信息
    # 在中断恢复时，all_files 需要重新构建，因为只保存已处理目录不保存文件列表
    # 对于未处理的目录，需要重新扫描
    all_files = {}  # 格式: {size: {name: [paths]}} 或 {size: {folder: {name: [paths]}}}

    # 扫描所有目录并收集文件信息
    logger.info("正在扫描目录并收集文件信息...")
    total_dirs = len(CONFIG['scan_directories'])
    completed_dirs_count = 0

    # 首先处理已标记为已处理的目录，将它们的文件信息重新收集（仅用于构建all_files）
    # 注意：这里重新收集已处理目录的文件信息是为了后续统一进行跨文件夹哈希对比
    # 如果是非跨文件夹模式，可以跳过已处理目录的文件收集
    dirs_to_process = []
    for directory in CONFIG['scan_directories']:
        # 规范化路径以便比较
        normalized_dir = os.path.normpath(directory)
        if normalized_dir in processed_directories:
             logger.info(f"加载状态：目录已处理，重新收集文件信息用于all_files构建: {directory}")
             # 重新收集文件信息，但不标记为已处理（因为它已经是）
             files_info, processed_count, skipped_count = collect_files(directory)
             logger.info(f"目录 {directory} 重新收集文件结果: 处理 {processed_count} 个文件，跳过 {skipped_count} 个文件")
             for file_path, file_size, file_name, folder in files_info:
                  try:
                       if file_size not in all_files:
                           all_files[file_size] = {}
                       if CONFIG['cross_folder_comparison']:
                            if file_name not in all_files[file_size]:
                                all_files[file_size][file_name] = []
                            all_files[file_size][file_name].append(file_path)
                       else:
                           if folder not in all_files[file_size]:
                                all_files[file_size][folder] = {}
                           if file_name not in all_files[file_size][folder]:
                                all_files[file_size][folder][file_name] = []
                           all_files[file_size][folder][file_name].append(file_path)
                  except Exception as e:
                        logger.error(f"重新处理文件时出错: {file_path}, 错误: {str(e)}")
             completed_dirs_count += 1
        else:
             dirs_to_process.append(directory) # 添加到待处理列表

    # 保存哈希缓存（可能在加载状态时已经加载，这里再保存一次确保最新）
    save_hash_cache()

    # 继续处理未完成的目录
    for dir_idx, directory in enumerate(dirs_to_process):
        normalized_dir = os.path.normpath(directory)
        if not os.path.exists(directory):
            logger.warning(f"目录不存在: {directory}")
            # 如果目录不存在，也将其标记为已处理，避免下次重复警告
            processed_directories.add(normalized_dir)
            save_state({
                'processed_directories': list(processed_directories),
                'duplicate_groups': duplicate_groups, # 保存当前找到的重复组（如果是非跨文件夹模式）
                'deleted_files_set': _deleted_files_set,
            }, f"处理不存在目录 {directory}")
            completed_dirs_count += 1
            continue # 跳过这个目录

        logger.info(f"扫描目录 ({completed_dirs_count + 1}/{total_dirs}): {directory}")

        # 收集文件信息
        files_info, processed_count, skipped_count = collect_files(directory)

        logger.info(f"目录 {directory} 文件过滤结果: 处理 {processed_count} 个文件，跳过 {skipped_count} 个文件")

        # 处理收集到的文件信息
        for file_path, file_size, file_name, folder in files_info:
            try:
                if file_size not in all_files:
                    all_files[file_size] = {}

                if CONFIG['cross_folder_comparison']:
                    # 跨文件夹模式：所有文件放在一起比较
                    if file_name not in all_files[file_size]:
                        all_files[file_size][file_name] = []
                    all_files[file_size][file_name].append(file_path)
                else:
                    # 非跨文件夹模式：按目录分组
                    if folder not in all_files[file_size]:
                        all_files[file_size][folder] = {}
                    if file_name not in all_files[file_size][folder]:
                        all_files[file_size][folder][file_name] = []
                    all_files[file_size][folder][file_name].append(file_path)
            except Exception as e:
                logger.error(f"处理文件时出错: {file_path}, 错误: {str(e)}")

        # 标记当前目录为已处理
        processed_directories.add(normalized_dir)
        completed_dirs_count += 1

        # 在处理完每个目录后保存一次状态和哈希缓存
        save_state({
            'processed_directories': list(processed_directories),
            'duplicate_groups': duplicate_groups, # 非跨文件夹模式下，每个目录处理完可能找到重复组
            'deleted_files_set': _deleted_files_set,
        }, f"完成目录扫描 {directory}")
        save_hash_cache()


    # 查找重复文件
    logger.info("正在查找重复文件...")

    if CONFIG['debug_mode']:
        if CONFIG['cross_folder_comparison']:
            total_files_by_size = sum(len(paths) for names in all_files.values() for paths in names.values())
        else:
            total_files_by_size = sum(len(paths) for folders in all_files.values() for files in folders.values() for paths in files.values())
        logger.info(f"按文件大小分组后，共有 {len(all_files)} 个不同大小的文件组，总计 {total_files_by_size} 个文件")

    # 查找重复文件的逻辑保持不变，但需要注意 duplicate_groups 是从状态中加载的（可能非空）
    # 跨文件夹模式或同文件夹模式，都统一使用哈希分组查找重复
    # 首先按文件大小分组 (已在collect_files中完成并存储到all_files)

    # 按文件大小遍历分组
    for size, names_or_folders in tqdm(all_files.items(), desc="按大小分组", unit="组"):
        # 对于每个大小分组，收集所有文件路径
        all_paths_in_size_group = []
        if CONFIG['cross_folder_comparison']:
             # 跨文件夹模式，all_files[size] 是 {name: [paths]}
             for name, paths in names_or_folders.items():
                  all_paths_in_size_group.extend(paths)
        else:
             # 同文件夹模式，all_files[size] 是 {folder: {name: [paths]}}
             for folder, names in names_or_folders.items():
                  for name, paths in names.items():
                       all_paths_in_size_group.extend(paths)

        # 如果该大小分组文件数大于1，则计算哈希并按哈希分组
        if len(all_paths_in_size_group) > 1:
             hash_groups = {}

             # 并行计算当前大小组的哈希值
             # 使用 ThreadPoolExecutor 处理 I/O 密集型任务
             with concurrent.futures.ThreadPoolExecutor() as executor:
                  # 提交哈希计算任务
                  hash_futures = {executor.submit(get_file_hash, file_path): file_path for file_path in all_paths_in_size_group}

                  # 使用 tqdm 显示并行任务进度
                  for future in tqdm(concurrent.futures.as_completed(hash_futures), total=len(all_paths_in_size_group), desc=f"并行计算哈希 (大小: {size} 字节)", leave=False, unit="文件"):
                       file_path = hash_futures[future]
                       try:
                            file_hash = future.result()
                            if file_hash:
                                 if file_hash not in hash_groups:
                                      hash_groups[file_hash] = []
                                 hash_groups[file_hash].append(file_path)
                       except Exception as exc:
                            logger.error(f'{file_path} 生成哈希时出错: {exc}')

             # 遍历哈希分组，找到真正的重复文件组
             for file_hash, paths in hash_groups.items():
                  if len(paths) > 1:
                       # 这是一个重复文件组 (内容相同)
                       # 使用智能选择策略决定保留哪个，删除哪些
                       # 注意：这里需要过滤掉已经添加到 deleted_files_set 中的文件
                       valid_paths = [p for p in paths if p not in _deleted_files_set]

                       if len(valid_paths) > 1:
                           # 仍然存在多个未删除的重复文件
                           original_file = valid_paths[0] # 默认第一个是原始文件，之后用智能选择调整
                           duplicate_files = valid_paths[1:]

                           for duplicate in duplicate_files:
                                # 使用智能选择策略判断最终的保留和删除对象
                                selected_original, selected_duplicate, reason, confidence, file_type = smart_file_selection(original_file, duplicate)

                                # 确保添加到待删除列表的是最终确定的副本文件，且未被添加过
                                if selected_duplicate not in _deleted_files_set:
                                    # 添加重复文件组信息，包括理由、置信度和类型
                                    # 使用 smart_file_selection 返回的理由、置信度和类型
                                    # smart_file_selection 返回的元组是 (original, duplicate, reason)，我们需要扩展它
                                    # is_duplicate_filename 返回 (is_duplicate, confidence, pattern_type)
                                    # 我们在 smart_file_selection 内部已经根据文件名判断更新了 reason
                                    # 所以这里直接使用 smart_file_selection 返回的 reason
                                    # 关于 confidence 和 type，需要在 smart_file_selection 中获取并传递回来
                                    # 或者在添加到 duplicate_groups 时，再次调用 is_duplicate_filename 来获取置信度和类型
                                    # 为了简化，我们修改 smart_file_selection 返回更多信息
                                    # 或者，在 find_duplicates 中添加时，根据 reason 字段的模式来判断置信度和类型

                                    # 当前 smart_file_selection 返回 (original, duplicate, reason)
                                    # 我们需要 reason, confidence, type
                                    # confidence 和 type 主要来自文件名模式判断
                                    # 如果 reason 来自文件名模式判断，我们可以解析它或者在 smart_file_selection 中直接返回

                                    # 优化方案：修改 smart_file_selection 返回 (original, duplicate, reason, confidence, type)
                                    # 暂时先使用现有结构，并在 process_duplicates 中根据 reason 查找信息（已实现部分）
                                    # 考虑到需要一次性修改，我将尝试修改 smart_file_selection 的返回值并在 find_duplicates 中使用

                                    # 新的 smart_file_selection 返回 (original, duplicate, reason, confidence, type)
                                    selected_original, selected_duplicate, reason, confidence, file_type = smart_file_selection(original_file, duplicate)

                                    _script_state['duplicate_groups'].append((selected_original, selected_duplicate, reason, confidence, file_type))
                                    _deleted_files_set.add(selected_duplicate) # 标记为待删除
                                    if CONFIG['file_selection_strategy']['show_selection_reason']:
                                        logger.info(f"添加重复文件组: 原始='{selected_original}', 副本='{selected_duplicate}', 理由='{reason}', 置信度='{confidence}', 类型='{file_type}'")
                                        # logger.info(f"  选择理由: {reason}") # 理由已包含在上面一行日志中

            # 在处理完一个哈希大小分组后保存状态
        save_state({
                'processed_directories': list(processed_directories),
                'duplicate_groups': _script_state['duplicate_groups'],
                'deleted_files_set': _deleted_files_set,
            }, f"处理完哈希大小组 {size}")

    logger.info(f"阶段 2 完成。共找到 {len(_script_state['duplicate_groups'])} 组潜在重复文件 (总计，包含阶段 1 找到的)。")

    # 处理重复文件
    if duplicate_groups:
        logger.info(f"找到 {len(duplicate_groups)} 组重复文件 (包含已加载的)")
        # process_duplicates 函数需要处理从状态中加载的重复组
        process_duplicates(duplicate_groups)
    else:
        logger.info("未找到新的重复文件")

    logger.info(f"查找重复文件完成，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 清理状态文件（如果所有重复文件都已处理）
    if not duplicate_groups:
        state_file = CONFIG['state_file']
        if os.path.exists(state_file):
            try:
                os.remove(state_file)
                logger.info(f"重复文件已处理完毕，已删除状态文件: {state_file}")
            except Exception as e:
                logger.warning(f"删除状态文件失败: {state_file}, 错误: {str(e)}")

    # 保存哈希缓存 (最后再保存一次)
    save_hash_cache()

# 判断是否应该自动删除
def should_auto_delete(original, duplicate):
    """根据智能规则判断是否应该自动删除"""
    _, _, reason = smart_file_selection(original, duplicate)

    for pattern in CONFIG['delete_mode']['smart_auto_patterns']:
        if pattern in reason:
            return True, reason
    return False, reason

# 显示文件详细信息
def show_file_info(file_path, label):
    try:
        stat = os.stat(file_path)
        size = stat.st_size
        mtime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))
        print(f"  {label}: {file_path}")
        print(f"    大小: {size:,} 字节")
        print(f"    修改时间: {mtime}")
        print(f"    目录: {os.path.dirname(file_path)}")
    except Exception as e:
        print(f"  {label}: {file_path} (无法获取详细信息: {e})")

# 处理重复文件
def process_duplicates(duplicate_groups):
    logger = logging.getLogger('duplicate_finder')
    delete_mode = CONFIG['delete_mode']['mode']

    print(f"\n删除模式: {delete_mode}")
    if delete_mode == 'auto':
        print("将自动删除所有重复文件")
    elif delete_mode == 'confirm_each':
        print("将逐个确认每个重复文件")
    elif delete_mode == 'batch_confirm':
        batch_size = CONFIG['delete_mode']['batch_size']
        print(f"将分批确认，每批 {batch_size} 个文件")
    elif delete_mode == 'smart_auto':
        print("将智能自动删除明确的重复文件，模糊情况下询问")

    deleted_count = 0
    skipped_count = 0
    # 跟踪本次运行中删除的文件数量

    # 从状态中加载已删除的文件集合
    global _deleted_files_set
    # 过滤掉已经从状态中加载的 deleted_files_set 中的重复文件组
    # duplicate_groups 现在包含 (original, duplicate, reason, confidence, type)
    # 我们只需要检查 duplicate 文件是否在已删除集合中
    pending_duplicate_groups = [
        group for group in duplicate_groups
        if group[1] not in _deleted_files_set
    ]

    print(f"从状态中加载已删除文件 {len(_deleted_files_set)} 个，待处理重复文件组 {len(pending_duplicate_groups)} 组")


    if delete_mode == 'batch_confirm':
        # 批量确认模式
        batch_size = CONFIG['delete_mode']['batch_size']
        for batch_start in range(0, len(pending_duplicate_groups), batch_size):
            batch_end = min(batch_start + batch_size, len(pending_duplicate_groups))
            batch = pending_duplicate_groups[batch_start:batch_end]

            print(f"\n" + "="*60)
            print(f"批次 {batch_start//batch_size + 1}: 显示第 {batch_start+1}-{batch_end} 个待处理重复文件组")
            print("="*60)

            # 显示这一批的所有文件
            for i, group in enumerate(batch):
                actual_index = batch_start + i + 1
                print(f"\n{actual_index}. 重复文件组:")
                show_file_info(group[0], "保留文件")
                show_file_info(group[1], "删除文件")

                # 显示选择理由
                if CONFIG['file_selection_strategy']['show_selection_reason']:
                    # 直接使用重复组元组中的理由、置信度和类型
                    original, duplicate, reason, confidence, file_type = group
                    print(f"  选择理由: {reason}")
                    if confidence != 'high': # 如果置信度不是高，也显示置信度和类型
                        print(f"    置信度: {confidence}, 类型: {file_type}")

            # 批量确认
            print(f"\n批量操作选项:")
            print(f"  a = 删除这批所有文件")
            print(f"  s = 跳过这批所有文件")
            print(f"  i = 逐个确认这批文件")
            print(f"  q = 退出处理并保存状态")

            batch_choice = input(f"请选择操作 (a/s/i/q): ").strip().lower()

            if batch_choice == 'q':
                print("用户选择退出")
                # 在退出前保存当前状态
                save_state({
                    'processed_directories': list(_script_state['processed_directories']),
                    'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                    'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                }, "用户批量确认退出")
                return # 退出函数
            elif batch_choice == 's':
                print(f"跳过批次 {batch_start//batch_size + 1} 的所有文件")
                skipped_count += len(batch)
                # 跳过不需要保存状态，因为这批文件还在 pending_duplicate_groups 中
                continue
            elif batch_choice == 'a':
                # 删除这批所有文件
                for i, group in enumerate(batch):
                    try:
                        os.remove(group[1])
                        deleted_count += 1
                        _deleted_files_set.add(group[1]) # 标记为已删除
                        logger.info(f"已删除重复文件: {group[1]}")
                    except OSError as e: # 捕获特定的文件操作错误
                        logger.error(f"删除文件时出错: {group[1]}, 错误: {e}")
                        # 可以选择跳过，或者记录待稍后重试
                        skipped_count += 1 # 计入跳过，因为未能成功删除
                        print(f"警告：未能删除文件 {group[1]}，错误：{e}")
                    except Exception as e: # 捕获其他未知错误
                        logger.error(f"删除文件时发生未知错误: {group[1]}, 错误: {e}")
                        skipped_count += 1
                        print(f"警告：未能删除文件 {group[1]}，发生未知错误：{e}")
                print(f"批次 {batch_start//batch_size + 1} 处理完成")
                # 在处理完一批后保存状态
                save_state({
                    'processed_directories': list(_script_state['processed_directories']),
                    'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                    'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                }, "处理完一批批量确认")
            elif batch_choice == 'i':
                # 逐个确认这批文件
                for i, group in enumerate(batch):
                    actual_index = batch_start + i + 1
                    print(f"\n确认删除第 {actual_index} 个重复文件:")
                    print(f"  删除: {group[1]}")
                    confirm = input("确认删除? (y/n/s=跳过剩余/q=退出): ").strip().lower()

                    if confirm == 'q':
                        print("用户选择退出")
                        # 在退出前保存当前状态
                        save_state({
                            'processed_directories': list(_script_state['processed_directories']),
                            'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                            'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                        }, "用户逐个确认退出")
                        return # 退出函数
                    elif confirm == 's':
                        print("跳过剩余文件")
                        skipped_count += len(batch) - i
                        # 跳过剩余不需要保存状态
                        break # 跳出当前批次的逐个确认循环
                    elif confirm == 'y':
                        try:
                            os.remove(group[1])
                            deleted_count += 1
                            _deleted_files_set.add(group[1]) # 标记为已删除
                            logger.info(f"用户确认已删除重复文件: {group[1]}")
                        except OSError as e: # 捕获特定的文件操作错误
                            logger.error(f"用户确认删除文件时出错: {group[1]}, 错误: {e}")
                            skipped_count += 1 # 计入跳过，因为未能成功删除
                            print(f"警告：未能删除文件 {group[1]}，错误：{e}")
                        except Exception as e: # 捕获其他未知错误
                            logger.error(f"用户确认删除文件时发生未知错误: {group[1]}, 错误: {e}")
                            skipped_count += 1
                            print(f"警告：未能删除文件 {group[1]}，发生未知错误：{e}")
                        else:
                            # 如果删除成功，不计入 skipped_count
                            pass
                    else:
                        skipped_count += 1
                        logger.info(f"用户选择不删除: {group[1]}")

                    # 每处理一个文件就保存一次状态（如果逐个确认）
                    save_state({
                        'processed_directories': list(_script_state['processed_directories']),
                        'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                        'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                    }, "处理一个逐个确认")
            else:
                print("无效选择，跳过此批次")
                skipped_count += len(batch)
                # 无效选择也不需要保存状态


    elif delete_mode == 'smart_auto':
        # 智能自动模式
        for i, group in enumerate(tqdm(pending_duplicate_groups, desc="智能处理重复文件", unit="组")):
            original, duplicate, reason, confidence, file_type = group

            auto_delete, _ = should_auto_delete(original, duplicate) # should_auto_delete 内部会重新计算理由，这里只取是否自动删除的判断结果

            if auto_delete:
                try:
                    os.remove(duplicate)
                    deleted_count += 1
                    _deleted_files_set.add(duplicate) # 标记为已删除
                    logger.info(f"自动删除重复文件: {duplicate} (理由: {reason})")
                except OSError as e: # 捕获特定的文件操作错误
                    logger.error(f"自动删除文件时出错: {duplicate}, 错误: {e}")
                    skipped_count += 1 # 计入跳过，因为未能成功删除
                    print(f"警告：未能自动删除文件 {duplicate}，错误：{e}")
                except Exception as e: # 捕获其他未知错误
                    logger.error(f"自动删除文件时发生未知错误: {duplicate}, 错误: {e}")
                    skipped_count += 1
                    print(f"警告：未能自动删除文件 {duplicate}，发生未知错误：{e}")
            else:
                # 需要用户确认
                print(f"\n需要确认的重复文件 ({i+1}/{len(pending_duplicate_groups)}):")
                show_file_info(original, "保留文件")
                show_file_info(duplicate, "删除文件")
                print(f"  选择理由: {reason}")
                if confidence != 'high': # 如果置信度不是高，也显示置信度和类型
                    print(f"    置信度: {confidence}, 类型: {file_type}")

                confirm = input("确认删除重复文件? (y/n/s=跳过剩余/q=退出): ").strip().lower()
                if confirm == 'q':
                    print("用户选择退出")
                    # 在退出前保存当前状态
                    save_state({
                        'processed_directories': list(_script_state['processed_directories']),
                        'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                        'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                    }, "用户智能模式退出")
                    return # 退出函数
                elif confirm == 's':
                    print("跳过剩余需要确认的文件")
                    skipped_count += len(pending_duplicate_groups) - i
                    # 跳过剩余不需要保存状态
                    break
                elif confirm == 'y':
                    try:
                        os.remove(duplicate)
                        deleted_count += 1
                        _deleted_files_set.add(duplicate) # 标记为已删除
                        logger.info(f"用户确认删除重复文件: {duplicate}")
                    except OSError as e: # 捕获特定的文件操作错误
                        logger.error(f"用户确认删除文件时出错: {duplicate}, 错误: {e}")
                        skipped_count += 1 # 计入跳过，因为未能成功删除
                        print(f"警告：未能删除文件 {duplicate}，错误：{e}")
                    except Exception as e: # 捕获其他未知错误
                        logger.error(f"用户确认删除文件时发生未知错误: {duplicate}, 错误: {e}")
                        skipped_count += 1
                        print(f"警告：未能删除文件 {duplicate}，发生未知错误：{e}")
                else:
                    skipped_count += 1
                    logger.info(f"用户选择不删除: {duplicate}")

            # 每处理或跳过一个需要确认的文件就保存一次状态
            save_state({
                'processed_directories': list(_script_state['processed_directories']),
                'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
            }, "处理一个智能模式文件")

    elif delete_mode == 'auto':
        # 自动删除模式
        for original, duplicate in tqdm(pending_duplicate_groups, desc="自动删除重复文件", unit="组"):
            try:
                os.remove(duplicate)
                deleted_count += 1
                _deleted_files_set.add(duplicate) # 标记为已删除
                logger.info(f"自动删除重复文件: {duplicate}")
            except OSError as e: # 捕获特定的文件操作错误
                logger.error(f"自动删除文件时出错: {duplicate}, 错误: {e}")
                skipped_count += 1 # 计入跳过，因为未能成功删除
                print(f"警告：未能自动删除文件 {duplicate}，错误：{e}")
            except Exception as e: # 捕获其他未知错误
                logger.error(f"自动删除文件时发生未知错误: {duplicate}, 错误: {e}")
                skipped_count += 1
                print(f"警告：未能自动删除文件 {duplicate}，发生未知错误：{e}")
            # 每删除一个文件就保存一次状态
            save_state({
                'processed_directories': list(_script_state['processed_directories']),
                'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
            }, "自动删除一个文件")


    else:  # confirm_each
        # 逐个确认模式
        for i, group in enumerate(pending_duplicate_groups):
            original, duplicate, reason, confidence, file_type = group

            print(f"\n重复文件 ({i+1}/{len(pending_duplicate_groups)}):")
            show_file_info(original, "保留文件")
            show_file_info(duplicate, "删除文件")

            if CONFIG['file_selection_strategy']['show_selection_reason']:
                print(f"  选择理由: {reason}")
                if confidence != 'high': # 如果置信度不是高，也显示置信度和类型
                    print(f"    置信度: {confidence}, 类型: {file_type}")

            confirm = input("确认删除重复文件? (y/n/s=跳过剩余/q=退出): ").strip().lower()
            if confirm == 'q':
                print("用户选择退出")
                # 在退出前保存当前状态
                save_state({
                    'processed_directories': list(_script_state['processed_directories']),
                    'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                    'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
                }, "用户逐个确认退出")
                return # 退出函数
            elif confirm == 's':
                print("跳过剩余文件")
                skipped_count += len(pending_duplicate_groups) - i
                # 跳过剩余不需要保存状态
                break # 跳出当前批次的逐个确认循环
            elif confirm == 'y':
                try:
                    os.remove(duplicate)
                    deleted_count += 1
                    _deleted_files_set.add(duplicate) # 标记为已删除
                    logger.info(f"用户确认已删除重复文件: {duplicate}")
                except OSError as e: # 捕获特定的文件操作错误
                    logger.error(f"用户确认删除文件时出错: {duplicate}, 错误: {e}")
                    skipped_count += 1 # 计入跳过，因为未能成功删除
                    print(f"警告：未能删除文件 {duplicate}，错误：{e}")
                except Exception as e: # 捕获其他未知错误
                    logger.error(f"用户确认删除文件时发生未知错误: {duplicate}, 错误: {e}")
                    skipped_count += 1
                    print(f"警告：未能删除文件 {duplicate}，发生未知错误：{e}")
            else:
                skipped_count += 1
                logger.info(f"用户选择不删除: {duplicate}")

            # 每处理一个文件就保存一次状态
            save_state({
                'processed_directories': list(_script_state['processed_directories']),
                'duplicate_groups': [g for g in duplicate_groups if g[1] not in _deleted_files_set], # 保存剩余待处理的重复组
                'deleted_files_set': list(_deleted_files_set), # 保存已删除的集合
            }, "处理一个逐个确认文件")


    # 显示处理结果 (仅显示本次运行的处理情况)
    print(f"\n" + "="*50)
    print(f"本次运行处理结果:")
    print(f"  已删除文件: {deleted_count} 个")
    print(f"  跳过文件: {skipped_count} 个")
    print(f"  待处理重复文件组总数: {len(duplicate_groups)} 组")
    print(f"  本次处理重复文件组数: {len(pending_duplicate_groups)} 组")
    print("="*50)

    # 在处理完成后清理状态文件
    if not pending_duplicate_groups:
        state_file = CONFIG['state_file']
        if os.path.exists(state_file):
            try:
                os.remove(state_file)
                logger.info(f"重复文件已处理完毕，已删除状态文件: {state_file}")
            except Exception as e:
                logger.warning(f"删除状态文件失败: {state_file}, 错误: {str(e)}")

if __name__ == "__main__":
    try:
        find_duplicates()
        # 在查找完成后，可以提示用户运行处理重复文件的函数
        print("\n重复文件查找完成。请运行 process_duplicates() 函数来处理重复文件。")
        # 或者如果配置是自动/智能模式，可以在这里直接调用 process_duplicates
        # 但为了清晰起见和中断恢复，分两步更合适

    except KeyboardInterrupt:
        print("\n操作被用户中断")
        logging.getLogger('duplicate_finder').info("操作被用户中断")
        # 在中断时保存当前状态
        try:
            save_state({
                'processed_directories': list(_script_state.get('processed_directories', [])), # 获取已处理目录
                'duplicate_groups': _script_state.get('duplicate_groups', []), # 保存当前找到的重复组
                'deleted_files_set': _deleted_files_set, # 保存已确认删除的集合
            }, "中断")
        except Exception as save_e:
            print(f"中断时保存状态失败: {save_e}")
            logging.getLogger('duplicate_finder').error(f"中断时保存状态失败: {save_e}")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        logging.getLogger('duplicate_finder').error(f"发生错误: {str(e)}")
